import os
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from langchain_openai.chat_models.base import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.schema import BaseOutputParser

# 常量定义
class GradeLevel(Enum):
    """评分等级枚举"""
    EXCELLENT = "优秀"
    GOOD = "良好"
    AVERAGE = "一般"
    POOR = "较差"
    ERROR = "解析错误"
    FAILED = "评分失败"


class ScoringConstants:
    """评分相关常量"""
    # 评分维度
    DIMENSIONS = ['综合分析', '语言表达', '逻辑思维', '专业知识', '应变能力', '价值取向']

    # 评分阈值
    EXCELLENT_THRESHOLD = 85
    GOOD_THRESHOLD = 75
    AVERAGE_THRESHOLD = 60

    # 默认值
    DEFAULT_QUESTION_TYPE = "综合分析题"
    DEFAULT_TEMPERATURE = 0.1

    # 正则表达式模式（预编译以提高性能）
    SCORE_PATTERN = re.compile(r"(综合分析|语言表达|逻辑思维|专业知识|应变能力|价值取向)[:：]\s*(\d+(?:\.\d+)?)")
    LIST_PATTERN_TEMPLATE = r"{}[:：]\s*(.*?)(?=优点|缺点|建议|$)"


@dataclass
class ScoringResult:
    """评分结果数据类"""
    综合分析: float
    语言表达: float
    逻辑思维: float
    专业知识: float
    应变能力: float
    价值取向: float
    总分: float
    等级: str
    优点: List[str]
    缺点: List[str]
    建议: List[str]

class InterviewScoringParser(BaseOutputParser):
    """自定义输出解析器"""

    def __init__(self):
        """初始化解析器，预编译正则表达式"""
        self._list_patterns = {
            category: re.compile(ScoringConstants.LIST_PATTERN_TEMPLATE.format(category), re.DOTALL)
            for category in ["优点", "缺点", "建议"]
        }

    def parse(self, text: str) -> ScoringResult:
        """解析LLM输出文本为评分结果"""
        try:
            # 提取各维度分数
            scores = self._extract_scores(text)

            # 计算总分
            total = self._calculate_total_score(scores)

            # 确定等级
            grade = self._determine_grade(total)

            # 提取优缺点和建议
            advantages = self._extract_list_optimized(text, "优点")
            disadvantages = self._extract_list_optimized(text, "缺点")
            suggestions = self._extract_list_optimized(text, "建议")

            return ScoringResult(
                综合分析=scores.get('综合分析', 0.0),
                语言表达=scores.get('语言表达', 0.0),
                逻辑思维=scores.get('逻辑思维', 0.0),
                专业知识=scores.get('专业知识', 0.0),
                应变能力=scores.get('应变能力', 0.0),
                价值取向=scores.get('价值取向', 0.0),
                总分=total,
                等级=grade,
                优点=advantages,
                缺点=disadvantages,
                建议=suggestions
            )
        except Exception as e:
            print(f"解析错误: {e}")
            return self._default_result()

    def _extract_scores(self, text: str) -> Dict[str, float]:
        """提取各维度分数"""
        scores = {}
        matches = ScoringConstants.SCORE_PATTERN.findall(text)

        for dimension, score_str in matches:
            try:
                scores[dimension] = float(score_str)
            except ValueError:
                scores[dimension] = 0.0

        # 确保所有维度都有分数
        for dim in ScoringConstants.DIMENSIONS:
            if dim not in scores:
                scores[dim] = 0.0

        return scores

    def _calculate_total_score(self, scores: Dict[str, float]) -> float:
        """计算总分"""
        if not scores:
            return 0.0
        return sum(scores.values()) / len(scores)

    def _determine_grade(self, total: float) -> str:
        """根据总分确定等级"""
        if total >= ScoringConstants.EXCELLENT_THRESHOLD:
            return GradeLevel.EXCELLENT.value
        elif total >= ScoringConstants.GOOD_THRESHOLD:
            return GradeLevel.GOOD.value
        elif total >= ScoringConstants.AVERAGE_THRESHOLD:
            return GradeLevel.AVERAGE.value
        else:
            return GradeLevel.POOR.value

    def _extract_list_optimized(self, text: str, category: str) -> List[str]:
        """优化的列表项提取方法"""
        pattern = self._list_patterns.get(category)
        if not pattern:
            return []

        match = pattern.search(text)
        if not match:
            return []

        content = match.group(1).strip()
        if not content:
            return []

        # 优化的项目提取逻辑
        items = []
        for line in content.split('\n'):
            line = line.strip()
            if not line:
                continue

            # 移除列表标记
            if line.startswith('- '):
                line = line[2:].strip()
            elif line.startswith('• '):
                line = line[2:].strip()
            elif line.startswith(('1.', '2.', '3.', '4.', '5.')):
                line = line[2:].strip()

            # 过滤掉其他类别的标题
            if line and not line.startswith(('缺点', '建议', '优点')):
                items.append(line)

        return items

    def _extract_list(self, text: str, category: str) -> List[str]:
        """保留原方法以确保向后兼容"""
        return self._extract_list_optimized(text, category)

    def _default_result(self) -> ScoringResult:
        """返回默认的错误结果"""
        return ScoringResult(
            综合分析=0.0, 语言表达=0.0, 逻辑思维=0.0,
            专业知识=0.0, 应变能力=0.0, 价值取向=0.0,
            总分=0.0, 等级=GradeLevel.ERROR.value,
            优点=[], 缺点=[], 建议=[]
        )

class InterviewScoringAgent:
    """公务员面试评分Agent"""

    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None,
                 model_name: str = "gpt-3.5-turbo", temperature: float = ScoringConstants.DEFAULT_TEMPERATURE):
        """
        初始化评分Agent

        Args:
            base_url: API基础URL
            api_key: API密钥
            model_name: 模型名称
            temperature: 温度参数
        """
        self.llm = ChatOpenAI(
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
            base_url=base_url or os.getenv("OPENAI_BASE_URL"),
            model_name=model_name,
            temperature=temperature
        )
        self.parser = InterviewScoringParser()
        self.prompt_template = self._create_prompt_template()
        self.chain = self.prompt_template | self.llm

    def _create_prompt_template(self) -> PromptTemplate:
        """创建评分提示模板"""
        template = """你是一位资深的公务员面试考官，具有丰富的面试评分经验。请根据以下标准对考生回答进行评分：

评分维度及权重：
1. 综合分析能力（20%）：对问题的理解深度、分析的全面性和准确性
2. 语言表达能力（20%）：表达清晰度、逻辑性、语言规范性
3. 逻辑思维能力（15%）：思路清晰、条理分明、逻辑严密
4. 专业知识水平（15%）：相关知识掌握程度、运用能力
5. 应变能力（15%）：面对问题的反应速度和处理方式
6. 价值取向（15%）：价值观正确、符合公务员职业要求

评分标准：
- 优秀（{excellent_threshold}-100分）：全面准确，逻辑清晰，表达流畅
- 良好（{good_threshold}-{excellent_minus_one}分）：基本准确，有一定逻辑，表达较好
- 一般（{average_threshold}-{good_minus_one}分）：部分准确，逻辑一般，表达尚可
- 较差（{average_threshold}分以下）：准确性差，逻辑混乱，表达不清

题目类型：{{question_type}}
面试题目：{{question}}
考生回答：{{answer}}

请按以下格式进行评分：

综合分析：XX分
语言表达：XX分
逻辑思维：XX分
专业知识：XX分
应变能力：XX分
价值取向：XX分

优点：
- 具体优点1
- 具体优点2

缺点：
- 具体缺点1
- 具体缺点2

建议：
- 改进建议1
- 改进建议2

请确保评分客观公正，理由充分。""".format(
            excellent_threshold=ScoringConstants.EXCELLENT_THRESHOLD,
            good_threshold=ScoringConstants.GOOD_THRESHOLD,
            average_threshold=ScoringConstants.AVERAGE_THRESHOLD,
            excellent_minus_one=ScoringConstants.EXCELLENT_THRESHOLD - 1,
            good_minus_one=ScoringConstants.GOOD_THRESHOLD - 1
        )

        return PromptTemplate(
            template=template,
            input_variables=["question_type", "question", "answer"]
        )

    def score_interview(self, question: str, answer: str,
                       question_type: str = ScoringConstants.DEFAULT_QUESTION_TYPE) -> ScoringResult:
        """
        对面试回答进行评分

        Args:
            question: 面试题目
            answer: 考生回答
            question_type: 题目类型

        Returns:
            ScoringResult: 评分结果
        """
        try:
            response = self.chain.invoke({
                "question_type": question_type,
                "question": question,
                "answer": answer
            })
            result = self.parser.parse(response.content)
            return result
        except Exception as e:
            print(f"评分过程出错: {e}")
            return self._create_failed_result()

    def batch_score(self, interview_data: List[Dict[str, str]]) -> List[ScoringResult]:
        """
        批量评分

        Args:
            interview_data: 面试数据列表，每个元素包含question、answer、type字段

        Returns:
            List[ScoringResult]: 评分结果列表
        """
        if not interview_data:
            return []

        results = []
        for data in interview_data:
            result = self.score_interview(
                question=data.get("question", ""),
                answer=data.get("answer", ""),
                question_type=data.get("type", ScoringConstants.DEFAULT_QUESTION_TYPE)
            )
            results.append(result)
        return results

    def _create_failed_result(self) -> ScoringResult:
        """创建评分失败的结果"""
        return ScoringResult(
            综合分析=0.0, 语言表达=0.0, 逻辑思维=0.0,
            专业知识=0.0, 应变能力=0.0, 价值取向=0.0,
            总分=0.0, 等级=GradeLevel.FAILED.value,
            优点=[], 缺点=[], 建议=[]
        )

    def generate_report(self, result: ScoringResult) -> str:
        """
        生成评分报告

        Args:
            result: 评分结果

        Returns:
            str: 格式化的评分报告
        """
        # 使用列表推导式和join优化字符串拼接
        advantages_text = '\n'.join(f"• {优点}" for 优点 in result.优点) if result.优点 else "无"
        disadvantages_text = '\n'.join(f"• {缺点}" for 缺点 in result.缺点) if result.缺点 else "无"
        suggestions_text = '\n'.join(f"• {建议}" for 建议 in result.建议) if result.建议 else "无"

        report = f"""=== 公务员面试评分报告 ===

总分：{result.总分:.1f}分
等级：{result.等级}

各维度得分：
• 综合分析：{result.综合分析}分
• 语言表达：{result.语言表达}分
• 逻辑思维：{result.逻辑思维}分
• 专业知识：{result.专业知识}分
• 应变能力：{result.应变能力}分
• 价值取向：{result.价值取向}分

主要优点：
{advantages_text}

需要改进：
{disadvantages_text}

改进建议：
{suggestions_text}"""

        return report

# 使用示例
def main():
    """主函数示例"""
    # 初始化Agent（注意：这里的API密钥和URL仅为示例）
    agent = InterviewScoringAgent(
        base_url="http://log-llm-gateway.basic.bigo.inner",
        api_key="sk-A2Cg4S5MqxA8DWiLKXxi2g",
        model_name="qwen2.5:32b"
    )

    # 示例题目和回答
    question = "当前，一些地方出现了'躺平'现象，请谈谈你的看法。"
    answer = """对于'躺平'现象，我认为需要客观理性地分析。

首先，'躺平'现象反映了部分年轻人面临的现实压力，包括就业压力、房价高企、生活成本上升等问题，这些都是客观存在的社会问题。

其次，我们要区分消极'躺平'和理性选择。有些人选择简单生活方式，追求内心平静，这是个人选择；但如果因为困难就放弃奋斗，这种消极态度是不可取的。

作为公务员，我认为应该：
1. 理解和关注年轻人的诉求，推动相关政策落实
2. 加强正面引导，弘扬奋斗精神
3. 为年轻人创造更好的发展机会和环境

总之，既要理解'躺平'现象产生的原因，也要积极引导，帮助年轻人树立正确的人生观和价值观。"""

    # 进行评分
    result = agent.score_interview(question, answer, "时政热点类")

    # 生成报告
    report = agent.generate_report(result)
    print(report)


if __name__ == "__main__":
    main()