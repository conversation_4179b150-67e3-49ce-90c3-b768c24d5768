import os
from typing import Dict, List, Any
from dataclasses import dataclass
from langchain_openai.chat_models.base import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.chains import <PERSON><PERSON>hain
from langchain.schema import BaseOutputParser
import json
import re

@dataclass
class ScoringResult:
    """评分结果数据类"""
    综合分析: float
    语言表达: float
    逻辑思维: float
    专业知识: float
    应变能力: float
    价值取向: float
    总分: float
    等级: str
    优点: List[str]
    缺点: List[str]
    建议: List[str]

class InterviewScoringParser(BaseOutputParser):
    """自定义输出解析器"""
    
    def parse(self, text: str) -> ScoringResult:
        try:
            # 提取各维度分数
            scores = {}
            dimensions = ['综合分析', '语言表达', '逻辑思维', '专业知识', '应变能力', '价值取向']
            
            for dim in dimensions:
                pattern = f"{dim}[:：]\\s*(\\d+(?:\\.\\d+)?)"
                match = re.search(pattern, text)
                scores[dim] = float(match.group(1)) if match else 0.0
            
            # 计算总分
            total = sum(scores.values()) / len(scores)
            
            # 确定等级
            if total >= 85:
                grade = "优秀"
            elif total >= 75:
                grade = "良好"
            elif total >= 60:
                grade = "一般"
            else:
                grade = "较差"
            
            # 提取优缺点和建议
            advantages = self._extract_list(text, "优点")
            disadvantages = self._extract_list(text, "缺点")
            suggestions = self._extract_list(text, "建议")
            
            return ScoringResult(
                综合分析=scores.get('综合分析', 0),
                语言表达=scores.get('语言表达', 0),
                逻辑思维=scores.get('逻辑思维', 0),
                专业知识=scores.get('专业知识', 0),
                应变能力=scores.get('应变能力', 0),
                价值取向=scores.get('价值取向', 0),
                总分=total,
                等级=grade,
                优点=advantages,
                缺点=disadvantages,
                建议=suggestions
            )
        except Exception as e:
            print(f"解析错误: {e}")
            return self._default_result()
    
    def _extract_list(self, text: str, category: str) -> List[str]:
        """提取列表项"""
        pattern = f"{category}[:：]\\s*(.*?)(?={category}|$)"
        match = re.search(pattern, text, re.DOTALL)
        if match:
            content = match.group(1).strip()
            items = [item.strip('- ').strip() for item in content.split('\n') if item.strip()]
            return [item for item in items if item and not item.startswith(('缺点', '建议', '优点'))]
        return []
    
    def _default_result(self) -> ScoringResult:
        return ScoringResult(0, 0, 0, 0, 0, 0, 0, "解析错误", [], [], [])

class InterviewScoringAgent:
    """公务员面试评分Agent"""
    
    def __init__(self, base_url: str = None, api_key: str = None, model_name: str = "gpt-3.5-turbo"):
        self.llm = ChatOpenAI(
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
            base_url=base_url or os.getenv("OPENAI_BASE_URL"),
            model_name=model_name,
            temperature=0.1
        )
        self.parser = InterviewScoringParser()
        self._setup_chain()
    
    def _setup_chain(self):
        """设置评分链"""
        prompt_template = """
你是一位资深的公务员面试考官，具有丰富的面试评分经验。请根据以下标准对考生回答进行评分：

评分维度及权重：
1. 综合分析能力（20%）：对问题的理解深度、分析的全面性和准确性
2. 语言表达能力（20%）：表达清晰度、逻辑性、语言规范性
3. 逻辑思维能力（15%）：思路清晰、条理分明、逻辑严密
4. 专业知识水平（15%）：相关知识掌握程度、运用能力
5. 应变能力（15%）：面对问题的反应速度和处理方式
6. 价值取向（15%）：价值观正确、符合公务员职业要求

评分标准：
- 优秀（85-100分）：全面准确，逻辑清晰，表达流畅
- 良好（75-84分）：基本准确，有一定逻辑，表达较好
- 一般（60-74分）：部分准确，逻辑一般，表达尚可
- 较差（60分以下）：准确性差，逻辑混乱，表达不清

题目类型：{question_type}
面试题目：{question}
考生回答：{answer}

请按以下格式进行评分：

综合分析：XX分
语言表达：XX分
逻辑思维：XX分
专业知识：XX分
应变能力：XX分
价值取向：XX分

优点：
- 具体优点1
- 具体优点2

缺点：
- 具体缺点1
- 具体缺点2

建议：
- 改进建议1
- 改进建议2

请确保评分客观公正，理由充分。
"""
        
        self.prompt = PromptTemplate(
            template=prompt_template,
            input_variables=["question_type", "question", "answer"]
        )
        
        self.chain = self.prompt | self.llm
    
    def score_interview(self, question: str, answer: str, question_type: str = "综合分析题") -> ScoringResult:
        """对面试回答进行评分"""
        try:
            response = self.chain.invoke({
                "question_type": question_type,
                "question": question,
                "answer": answer
            })
            result = self.parser.parse(response.content)
            return result
        except Exception as e:
            print(f"评分过程出错: {e}")
            return ScoringResult(0, 0, 0, 0, 0, 0, 0, "评分失败", [], [], [])
    
    def batch_score(self, interview_data: List[Dict[str, str]]) -> List[ScoringResult]:
        """批量评分"""
        results = []
        for data in interview_data:
            result = self.score_interview(
                question=data.get("question", ""),
                answer=data.get("answer", ""),
                question_type=data.get("type", "综合分析题")
            )
            results.append(result)
        return results
    
    def generate_report(self, result: ScoringResult) -> str:
        """生成评分报告"""
        report = f"""
=== 公务员面试评分报告 ===

总分：{result.总分:.1f}分
等级：{result.等级}

各维度得分：
• 综合分析：{result.综合分析}分
• 语言表达：{result.语言表达}分
• 逻辑思维：{result.逻辑思维}分
• 专业知识：{result.专业知识}分
• 应变能力：{result.应变能力}分
• 价值取向：{result.价值取向}分

主要优点：
{chr(10).join([f"• {优点}" for 优点 in result.优点])}

需要改进：
{chr(10).join([f"• {缺点}" for 缺点 in result.缺点])}

改进建议：
{chr(10).join([f"• {建议}" for 建议 in result.建议])}
"""
        return report

# 使用示例
def main():
    # 初始化Agent
    agent = InterviewScoringAgent(base_url="http://log-llm-gateway.basic.bigo.inner", api_key="sk-A2Cg4S5MqxA8DWiLKXxi2g", model_name="qwen2.5:32b")
    
    # 示例题目和回答
    question = "当前，一些地方出现了'躺平'现象，请谈谈你的看法。"
    answer = """
    对于'躺平'现象，我认为需要客观理性地分析。
    
    首先，'躺平'现象反映了部分年轻人面临的现实压力，包括就业压力、房价高企、生活成本上升等问题，这些都是客观存在的社会问题。
    
    其次，我们要区分消极'躺平'和理性选择。有些人选择简单生活方式，追求内心平静，这是个人选择；但如果因为困难就放弃奋斗，这种消极态度是不可取的。
    
    作为公务员，我认为应该：
    1. 理解和关注年轻人的诉求，推动相关政策落实
    2. 加强正面引导，弘扬奋斗精神
    3. 为年轻人创造更好的发展机会和环境
    
    总之，既要理解'躺平'现象产生的原因，也要积极引导，帮助年轻人树立正确的人生观和价值观。
    """
    
    # 进行评分
    result = agent.score_interview(question, answer, "时政热点类")
    
    # 生成报告
    report = agent.generate_report(result)
    print(report)

if __name__ == "__main__":
    main()